import { formBindingData, FormExtensionAbility, formInfo, formProvider } from '@kit.FormKit';
import { Want } from '@kit.AbilityKit';
import { LocalPreferences } from '../utils/LocalPreferences';
import { randomExam } from '../api/exam';
import { AxiosResponse } from '@ohos/axios';
import { ExamModel } from '../models/ExamModel';
import Logger from '../utils/Logger';
import { BusinessError } from '@kit.BasicServicesKit';

const TAG = 'EntryFormAbility';

export default class EntryFormAbility extends FormExtensionAbility {
  // 当卡片被添加到桌面时触发
  onAddForm(want: Want) {
    // 将当前上下文赋值给 LocalPreferences，方便本地存储使用
    LocalPreferences.context = this.context;
    // 获取卡片的尺寸参数
    const formDimension: number =
      (want.parameters as Record<string, Object>)[formInfo.FormParam.DIMENSION_KEY] as number;

    // 获取卡片的唯一标识 ID
    const formId = (want.parameters as Record<string, Object>)[formInfo.FormParam.IDENTITY_KEY] as string;

    // 根据尺寸判断是否需要加载试卷列表
    if (formDimension === formInfo.FormDimension.Dimension_4_4) {
      this.getExamList(formId); // 调用方法获取试卷数据并更新卡片
    }

    return formBindingData.createFormBindingData(''); // 返回初始化的空数据
  }

  // 异步获取试卷列表并更新卡片内容
  async getExamList(formId: string) {
    try {
      const res: AxiosResponse = await randomExam(3); // 发送 API 请求，获取 3 个随机试卷
      let examList = res.data as ExamModel[]; // 将返回的数据转换为 ExamModel 数组
      this.updateForm(formId, new Object({
        examList: examList
      })) // 更新卡片内容
    } catch (error) {
      Logger.error(TAG, `Failed to load exam list: ${error}`); // 若数据加载失败，记录错误信息
    }
  }

  // 更新卡片内容的方法，接收卡片 ID 和新的数据对象
  updateForm(formId: string, formData: object) {
    // 创建数据绑定对象
    let formInfo: formBindingData.FormBindingData = formBindingData.createFormBindingData(formData);
    formProvider.updateForm(formId, formInfo).then(() => {
      console.log('FormAbility updateForm success.'); // 更新成功时记录日志
    }).catch((error: BusinessError) => {
      // 记录更新失败的错误信息
      console.log(`Operation updateForm failed. Cause: ${JSON.stringify(error)}`);
    });
  }

  onCastToNormalForm(formId: string) {
    // Called when the form provider is notified that a temporary form is successfully
    // converted to a normal form.
  }

  onUpdateForm(formId: string) {
    // Called to notify the form provider to update a specified form.
  }

  onFormEvent(formId: string, message: string) {
    // Called when a specified message event defined by the form provider is triggered.
  }

  onRemoveForm(formId: string) {
    // Called to notify the form provider that a specified form has been destroyed.
  }

  onAcquireFormState(want: Want) {
    // Called to return a {@link FormState} object.
    return formInfo.FormState.READY;
  }
};