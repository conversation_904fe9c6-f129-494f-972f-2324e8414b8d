export class Constants {
  // static readonly SERVER: string = 'http://192.168.1.19:3000';
  static readonly SERVER: string = 'https://api.admit-pro.com';
  static readonly TITLE_FONT_SIZE = 25
  static readonly BOLD_WEIGHT = 700
  static readonly PREFIX_ICON_WIDTH = 15
  static readonly LOGIN_INPUT_MAX_LEN = 20
  static readonly INPUT_HEIGHT = 40
  static readonly SPACE_SIZE = 14
  static readonly LIGHT_GRAY = '#f5f5f5'
  static readonly NAV_HEIGHT = 120;
  static readonly PANEL_SIZE = 35;
  static readonly EXAM_ITEM_HEIGHT = 65;
  static readonly TAB_TEXT_SIZE = 18;

  static readonly Column_PADDING = 15;
  static readonly NORMAL_SIZE = 14;
  static readonly SUBTITLE_HEIGHT = 50;
  static readonly QUESTION_TYPE_WIDTH = 45;
  static readonly GRID_ITEM_HEIGHT = 40;
  static readonly SMALL_SPACE = 5;

  // 公共常量
  static readonly ITEM_SPACE_SMALL = 5
  static readonly ITEM_SPACE = 10
  static readonly ITEM_SPACE_LARGE = 20;
  static readonly FULL_PARENT = '100%';
  static readonly MARGIN_10 = 10;
  static readonly MARGIN_15 = 15;
  static readonly MARGIN_20 = 20;
  static readonly MARGIN_25 = 25;
  static readonly MARGIN_40 = 40;
  static readonly PADDING_20 = 20;
  static readonly PADDING_14 = 14;

  // 登录页面常量
  static readonly LOGIN_PAGE_PADDING = 40;
  static readonly LOGO_WIDTH = 100;
  static readonly TITLE_SIZE = 25;
  static readonly LOGO_TITLE_TOP_MARGIN = 10;
  static readonly LOGO_TITLE_BOTTOM_MARGIN = 30;
  static readonly NORMAL_TEXT_FONT_SIZE = 18;
  static readonly BUTTON_HEIGHT = 50;
  static readonly BUTTON_FONT_SIZE = 16;
  static readonly LOGIN_PAGE_PATH = 'pages/Account/LoginPage';

  // 用户中心常量
  static readonly USER_ICON_WIDTH = 100;
  static readonly USER_NAME_SIZE = 18;
  static readonly USER_INFO_TOP_MARGIN = 60;
  static readonly USER_CENTER_WIDTH = '80%';
  static readonly ANSWER_COUNT_HEIGHT = 80;
  static readonly ANSWER_COUNT_RADIUS = 10;
  static readonly ANSWER_COUNT_TOP_MARGIN = 20;
  static readonly ANSWER_COUNT_SIZE = 24;
  static readonly ANSWER_COUNT_COLOR = '#4d79ed';
  static readonly DIVIDER_HEIGHT = '50%';
  static readonly DIVIDER_WIDTH = 1;
  static readonly WRONG_COUNT_COLOR = '#f00';
  static readonly BORDER_WIDTH = 1;
  static readonly PADDING_TOP = 10;
  static readonly PADDING_BOTTOM = 10;
  static readonly FONT_SIZE_LARGE_TEXT = '大';
  static readonly FONT_SIZE_MEDIUM_TEXT = '中';
  static readonly FONT_SIZE_SMALL_TEXT = '小';
  static readonly FONT_LARGE = 2;
  static readonly FONT_MEDIUM = 0;
  static readonly FONT_SMALL = -2;
  static readonly WRONG_COUNT_FONT_SIZE: number = 20;
  static readonly SELECT_FONT_SIZE = 18;

  static readonly LOGOUT_FONT_SIZE = 20;
  static readonly FONT_SETTING_SIZE = 20;
  static readonly LOGOUT_ARROW_WIDTH = 20;

  // 首页常量
  static readonly HOME_PATH = 'pages/Index' ;
  static readonly TAB_HEIGHT = 46;
  static readonly HOME_EXAM_TITLE_SIZE = 20;
  static readonly SWIPER_IMAGE_HEIGHT = 330;
  static readonly HOME_EXAM_TITLE_MARGIN = 20;
  static readonly EXAM_LIST_PADDING = 15;
  static readonly EXAM_LIST_ITEM_HEIGHT = 120;
  static readonly EXAM_LIST_ITEM_PADDING = 10;
  static readonly EXAM_ITEM_BORDER_RADIUS = 20;
  static readonly EXAM_ITEM_MARGIN = 10;
  static readonly EXAM_ITEM_BUTTON_HEIGHT = 35;
  static readonly EXAM_ITEM_BUTTON_FONT_SIZE = 14;
  static readonly EXAM_ITEM_BUTTON_WIDTH = 100;
  static readonly EXAM_ITEM_TITLE_FONT_SIZE = 14;
  static readonly EXAM_ITEM_PROGRESS_FONT_SIZE = 12;

  // 科目列表常量
  static readonly SUBJECT_TITLE_FONT_SIZE = 30;
  static readonly GRID_COLUMNS_GAP_SM = 10;
  static readonly GRID_ROWS_GAP_SM = 20;
  static readonly PADDING_SM_8 = 8;
  static readonly WIDTH_80 = 80;
  static readonly BORDER_RADIUS_5 = 5;
  static readonly FONT_SIZE_10 = 10;
  static readonly SUBJECT_ITEM_TITLE_FONT_SIZE = 18;
  static readonly SUBJECT_ITEM_PROGRESS_FONT_SIZE = 12;

  // 题目详情常量
  static readonly QUESTION_TEXT_SIZE = 18;
  static readonly QUESTION_OPTION_TEXT_SIZE = 18;
  static readonly QUESTION_OPTION_HEIGHT = 50;
  static readonly QUESTION_OPTION_BORDER_RADIUS = 10;
  static readonly QUESTION_OPTION_PADDING = 20;
  static readonly ARROW_WIDTH = 55;
  static readonly SUBMIT_BUTTON_WIDTH = 75;
  static readonly SUBMIT_BUTTON_HEIGHT = 40;

  // 答题结果常量
  static readonly ANSWER_RESULT_PANEL_RADIUS = 20;
  static readonly SCORE_TITLE_FONT_SIZE = 24;
  static readonly SCORE_FONT_SIZE = 45;
  static readonly ANSWER_DATA_WIDTH = 120;
  static readonly ANSWER_CARD_TITLE_HEIGHT = 40;
  static readonly ANSWER_CARD_ITEM_HEIGHT = 30;
  static readonly ANSWER_CARD_ITEM_RADIUS = 5;
  static readonly GRID_ITEM_COLUMNS_GAP = 20;
  static readonly GRID_ITEM_ROWS_GAP = 10;
  static readonly RETRY_BUTTON_WIDTH = 200;
  static readonly RETRY_BUTTON_HEIGHT = 40;
  static readonly ANSWER_RIGHT = 1;
  static readonly ANSWER_WRONG = 0;
  static readonly ANSWER_CARD_FONT_SIZE = 20;

  // 首选项常量
  static readonly STORE_NAME = 'preferences_shuati'
  static readonly PREFER_FONT_SIZE_KEY = "prefer_font_size";

  // 首选项
  static readonly STORAGE_FONT_SIZE_KEY = "storage_font_size";
  static readonly  TOKEN_KEY = 'shuati_token';
  static readonly USER_KEY = 'user_info'

}