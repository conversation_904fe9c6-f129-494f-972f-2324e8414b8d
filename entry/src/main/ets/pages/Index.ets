import { Constants } from '../common/Constants'
import { AnswerResult } from './ExamSubject/AnswerResult';
import { Exam } from './ExamSubject/Exam';
import { ExamDetail } from './ExamSubject/ExamDetail';
import { ExamSubject } from './ExamSubject/ExamSubject';
import { Home } from './Home/Home';
import { UserCenter } from './UserCenter/UserCenter';

let storage = LocalStorage.getShared()

@Entry(storage)
@Component
struct Index {
  @State currentIndex: number = 0;
  @Provide pageStack: NavPathStack = new NavPathStack();

  @LocalStorageProp(Constants.STORAGE_FONT_SIZE_KEY) fontSize: number = 0

  @LocalStorageProp('selectPage') selectPage: string = '';

  aboutToAppear(): void {
    // 从 LocalStorage 获取 examId 变量，该变量用于存储选中的试卷 ID
    const examId = storage.get<number>('examId');
    // 如果 selectPage 存在，则表示需要跳转到特定页面
    if (this.selectPage) {
      // 将 selectPage 和 examId 作为参数推送到导航堆栈中，触发页面跳转
      this.pageStack.pushPath({
        name: this.selectPage,  // 指定跳转的页面名称
        param: examId           // 传递试卷 ID 参数
      });
    }
  }


  build() {
    Navigation(this.pageStack) {
      Tabs({
        barPosition: BarPosition.End,
        index: this.currentIndex
      }) {
        TabContent() {
          Home()
        }
        .tabBar(this.TabBuilder($r('app.string.recommend'), $r('app.media.home'), $r('app.media.home_active'), 0))

        TabContent() {
          ExamSubject()
        }
        .tabBar(this.TabBuilder($r('app.string.exam'), $r('app.media.exam'), $r('app.media.exam_active'), 1))

        TabContent() {
          UserCenter()
        }
        .tabBar(this.TabBuilder($r('app.string.user_center'), $r('app.media.user'), $r('app.media.user_active'), 2))
      }
      .width(Constants.FULL_PARENT)
      .height(Constants.FULL_PARENT)
      .backgroundColor(Color.White)
      .backgroundColor($r('app.color.tab_bg_color'))
      .onChange((index: number) => {
        this.currentIndex = index
      })
    }
    .hideTitleBar(true)
    .mode(NavigationMode.Stack)
    .navDestination(this.PageMap) // 配置页面路由
  }

  @Builder
  PageMap(name: string) {
    if (name === 'Exam') {
      Exam()
    } else if (name === 'ExamDetail') {
      ExamDetail()
    } else if (name === 'AnswerResult') {
      AnswerResult()
    }
  }

  @Builder
  TabBuilder(title: ResourceStr, icon: ResourceStr, activeIcon: ResourceStr, index: number) {
    Row() {
      if (this.currentIndex === index) {
        Image(activeIcon)
          .objectFit(ImageFit.Contain)
      } else {
        Column({ space: Constants.ITEM_SPACE_SMALL }) {
          Image(icon)
            .objectFit(ImageFit.Contain)
            .aspectRatio(1)
            .layoutWeight(1)
          Text(title)
            .fontColor($r('app.color.tab_text_color'))
            .fontSize(Constants.TAB_TEXT_SIZE + this.fontSize)
        }
      }
    }
    .height(Constants.TAB_HEIGHT)
  }
}