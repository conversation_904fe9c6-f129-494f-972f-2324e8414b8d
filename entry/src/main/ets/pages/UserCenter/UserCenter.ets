import { Constants } from '../../common/Constants'
import { promptAction, router } from '@kit.ArkUI'
import { LocalPreferences } from '../../utils/LocalPreferences'
import { UserInfoModel } from '../../models/UserInfoModel';
import { AxiosResponse } from '@ohos/axios';
import { getUserAnswerCount } from '../../api/user';

class AnswerCount {
  total_answers: number = 0
  wrong_answers: number = 0
}

@Component
export struct UserCenter {
  @LocalStorageProp(Constants.STORAGE_FONT_SIZE_KEY) fontSize: number = 0;
  @State index: number = 1;
  @State text: string = Constants.FONT_SIZE_MEDIUM_TEXT;
  userInfo: UserInfoModel = new UserInfoModel()
  @State answerCount: AnswerCount = new AnswerCount()

  async aboutToAppear(): Promise<void> {
    this.initFontSizeSetting();
    this.userInfo = LocalPreferences.getUserInfo()
    const res: AxiosResponse = await getUserAnswerCount()
    this.answerCount = res.data[0]
  }

  initFontSizeSetting(): void {
    let fontSize = LocalPreferences.getFontSize();
    switch (fontSize) {
      case Constants.FONT_LARGE:
        this.index = 0;
        this.text = Constants.FONT_SIZE_LARGE_TEXT;
        break;
      case Constants.FONT_MEDIUM:
        this.index = 1;
        this.text = Constants.FONT_SIZE_MEDIUM_TEXT;
        break;
      case Constants.FONT_SMALL:
        this.index = 2;
        this.text = Constants.FONT_SIZE_SMALL_TEXT;
        break;
    }
  }

  build() {

    RelativeContainer() {
      Column({ space: Constants.ITEM_SPACE_SMALL }) {
        Image($r('app.media.user_icon'))
          .width(Constants.USER_ICON_WIDTH)
        Text(this.userInfo.username)
          .fontColor(Color.White)
          .fontSize(Constants.USER_NAME_SIZE + this.fontSize)
      }
      .id('user_info')
      .alignRules({
        middle: { anchor: '__container__', align: HorizontalAlign.Center },
        top: { anchor: '__container__', align: VerticalAlign.Top }
      })
      .margin({
        top: Constants.USER_INFO_TOP_MARGIN
      })

      Row() {
        Column({ space: Constants.ITEM_SPACE }) {
          Text(`${this.answerCount.total_answers}`)
            .fontSize(Constants.ANSWER_COUNT_SIZE + this.fontSize)
            .fontWeight(FontWeight.Bold)
            .fontColor(Constants.ANSWER_COUNT_COLOR)
          Text($r('app.string.answered_questions'))
            .fontSize(Constants.WRONG_COUNT_FONT_SIZE + this.fontSize)
        }

        Divider()
          .height(Constants.DIVIDER_HEIGHT)
          .width(Constants.DIVIDER_WIDTH)
          .vertical(true)

        Column({ space: Constants.ITEM_SPACE }) {
          Text(`${this.answerCount.wrong_answers}`)
            .fontSize(Constants.ANSWER_COUNT_SIZE + this.fontSize)
            .fontWeight(FontWeight.Bold)
            .fontColor(Constants.WRONG_COUNT_COLOR)
          Text($r('app.string.wrong_questions'))
            .fontSize(Constants.WRONG_COUNT_FONT_SIZE + this.fontSize)
        }
      }
      .id('answer_count')
      .width(Constants.USER_CENTER_WIDTH)
      .height(Constants.ANSWER_COUNT_HEIGHT)
      .backgroundColor(Color.White)
      .borderRadius(Constants.ANSWER_COUNT_RADIUS)
      .alignRules({
        middle: { anchor: 'user_info', align: HorizontalAlign.Center },
        top: { anchor: 'user_info', align: VerticalAlign.Bottom }
      })
      .margin({
        top: Constants.ANSWER_COUNT_TOP_MARGIN
      })
      .justifyContent(FlexAlign.SpaceAround)

      Row() {
        Text($r('app.string.font_size_setting'))
          .fontSize(Constants.FONT_SETTING_SIZE + this.fontSize)
          .fontColor(Color.White)
        Select([
          { value: Constants.FONT_SIZE_LARGE_TEXT },
          { value: Constants.FONT_SIZE_MEDIUM_TEXT },
          { value: Constants.FONT_SIZE_SMALL_TEXT }
        ])
          .selected(this.index)
          .font({ size: Constants.SELECT_FONT_SIZE + this.fontSize })
          .value(this.text)
          .fontColor(Color.White)
          .backgroundColor(Color.Transparent)
          .onSelect((index: number, value: string) => {
            this.index = index;
            this.text = value;
            switch (index) {
              case 0:
                LocalPreferences.setFontSize(Constants.FONT_LARGE)
                break
              case 1:
                LocalPreferences.setFontSize(Constants.FONT_MEDIUM)
                break
              case 2:
                LocalPreferences.setFontSize(Constants.FONT_SMALL)
            }
          })
      }
      .width(Constants.USER_CENTER_WIDTH)
      .justifyContent(FlexAlign.SpaceBetween)
      .borderWidth({
        top: Constants.BORDER_WIDTH,
        bottom: Constants.BORDER_WIDTH
      })
      .borderColor(Color.White)
      .padding({
        top: Constants.PADDING_TOP,
        bottom: Constants.PADDING_BOTTOM
      })
      .margin({
        top: Constants.MARGIN_40
      })
      .alignRules({
        top: { anchor: 'answer_count', align: VerticalAlign.Bottom },
        middle: { anchor: '__container__', align: HorizontalAlign.Center }
      })
      .id('font_setting')

      Row() {
        Text('登出')
          .fontSize(Constants.LOGOUT_FONT_SIZE + this.fontSize)
          .fontColor('#fff')
        Image($r('app.media.ic_arrow_right'))
          .width(Constants.LOGOUT_ARROW_WIDTH)
      }
      .width(Constants.USER_CENTER_WIDTH)
      .justifyContent(FlexAlign.SpaceBetween)
      .borderWidth({
        bottom: Constants.BORDER_WIDTH
      })
      .borderColor(Color.White)
      .padding({
        top: Constants.PADDING_TOP,
        bottom: Constants.PADDING_BOTTOM
      })
      .alignRules({
        top: { anchor: 'font_setting', align: VerticalAlign.Bottom },
        middle: { anchor: '__container__', align: HorizontalAlign.Center }
      })
      .onClick(() => {
        LocalPreferences.removeToken()
        LocalPreferences.removeUserInfo()
        promptAction.showToast({ message: '已退出当前用户！', duration: 1000 })
        router.replaceUrl({
          url: 'pages/Account/LoginPage'
        })
      })

    }
    .width(Constants.FULL_PARENT)
    .backgroundColor($r('app.color.user_bgc'))
    .backgroundImage($r('app.media.user_bg'))
    .backgroundImageSize(ImageSize.FILL)

  }
}
