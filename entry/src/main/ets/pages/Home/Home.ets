import { AxiosResponse } from "@ohos/axios";
import { randomExam } from "../../api/exam";
import { Constants } from "../../common/Constants"
import { ExamModel } from "../../models/ExamModel"
import { ExamItem } from "./ExamItem"
import { BusinessError } from "@kit.BasicServicesKit";
import Logger from "../../utils/Logger";

const TAG = 'Home'

@Component
export struct Home {

  @LocalStorageProp(Constants.STORAGE_FONT_SIZE_KEY) fontSize: number = 0;

  // 定义图片数组
  private swiperImages: Resource[] = [
    $r('app.media.swiper1'),
    $r('app.media.swiper2'),
    $r('app.media.swiper3'),
    $r('app.media.swiper4'),
    $r('app.media.swiper5'),
    $r('app.media.swiper6')
  ]

  @State ExamList: ExamModel[] = [
    {
      id: 1,
      subject_id: 1,
      title: '鸿蒙认证高级-模拟题',
      score: 75,
      total_questions: 60,
      answered_questions: 30
    },
    {
      id: 2,
      subject_id: 2,
      title: '鸿蒙认证中级-模拟题',
      score: 95,
      total_questions: 60,
      answered_questions: 45
    },
    {
      id: 3,
      subject_id: 3,
      title: '鸿蒙认证初级-模拟题',
      score: 100,
      total_questions: 60,
      answered_questions: 60
    }
  ]

  async aboutToAppear(): Promise<void> {
    try {
      console.log('Home: Loading exam data...')
      const res: AxiosResponse = await randomExam(5)
      this.ExamList = res.data
      console.log('Home: Exam data loaded:', this.ExamList.length, 'items')
    } catch (e) {
      console.error('Home: Error loading exam data:', e)
      Logger.error(TAG, 'Error in randomExam', JSON.stringify(e as BusinessError))
    }
  }

  build() {
    Column({ space: Constants.ITEM_SPACE_LARGE}) {
      Swiper() {
        ForEach(this.swiperImages, (item: Resource) => {
          Image(item)
            .width(Constants.FULL_PARENT)
            .height(Constants.SWIPER_IMAGE_HEIGHT)
        })
      }
      .itemSpace(Constants.ITEM_SPACE_LARGE)
      .autoPlay(true)

      Text($r('app.string.random_exam_title'))
        .width(Constants.FULL_PARENT)
        .fontSize(Constants.HOME_EXAM_TITLE_SIZE + this.fontSize)
        .fontWeight(FontWeight.Bold)
        .fontColor(Color.White)
        .margin({
          left: Constants.HOME_EXAM_TITLE_MARGIN
        })

      List() {
        ForEach(this.ExamList, (item: ExamModel) => {
          ListItem() {
            ExamItem({ exam: item })
          }
        })
      }
      .width(Constants.FULL_PARENT)
      .height(Constants.FULL_PARENT)
      .layoutWeight(1)
      .backgroundColor($r('app.color.tab_bg_color'))
      .padding(Constants.EXAM_LIST_PADDING)
    }
    .width(Constants.FULL_PARENT)
    .height(Constants.FULL_PARENT)
    .backgroundColor($r('app.color.home_bg_color'))

  }
}