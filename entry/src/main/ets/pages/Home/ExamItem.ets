import { Constants } from '../../common/Constants'
import { ExamModel } from '../../models/ExamModel'

@Component
export struct ExamItem {
  @Consume pageStack: NavPathStack
  @LocalStorageProp(Constants.STORAGE_FONT_SIZE_KEY) fontSize: number = 0;

  exam: ExamModel = {
    id: 1,
    subject_id: 1,
    title: '鸿蒙认证高级-模拟题',
    score: 75,
    total_questions: 30,
    answered_questions: 10
  }

  build() {
    Row() {
      Image($r('app.media.exam_icon'))
        .height(Constants.FULL_PARENT)

      Column() {
        Text(this.exam.title)
          .fontSize(Constants.EXAM_ITEM_TITLE_FONT_SIZE + this.fontSize)
          .fontWeight(FontWeight.Bold)

        Column() {
          Text(`${this.exam.answered_questions}/${this.exam.total_questions}节|已学进度：${Math.floor(this.exam.answered_questions /
          this.exam.total_questions * 100)}%`)
            .fontColor($r('app.color.progress_text_color'))
            .fontSize(Constants.EXAM_ITEM_PROGRESS_FONT_SIZE + this.fontSize)
            .width(Constants.FULL_PARENT)
          Progress({ value: this.exam.answered_questions, total: this.exam.total_questions })
        }
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)
      .justifyContent(FlexAlign.SpaceBetween)
      .height(Constants.FULL_PARENT)
      .margin({ left: Constants.EXAM_ITEM_MARGIN, right: Constants.EXAM_ITEM_MARGIN })

      Button($r('app.string.begin_exam'))
        .width(Constants.EXAM_ITEM_BUTTON_WIDTH)
        .height(Constants.EXAM_ITEM_BUTTON_HEIGHT)
        .fontSize(Constants.EXAM_ITEM_BUTTON_FONT_SIZE + this.fontSize)
        .backgroundColor($r('app.color.exam_button_bgc'))
        .onClick(() => {
          console.log('ExamItem clicked, exam id:', this.exam.id)
          this.pageStack.pushPath({
            name: 'ExamDetail',
            param: this.exam.id
          })
        })
    }
    .width(Constants.FULL_PARENT)
    .height(Constants.EXAM_LIST_ITEM_HEIGHT)
    .alignItems(VerticalAlign.Bottom)
    .padding(Constants.EXAM_LIST_ITEM_PADDING)
    .backgroundColor(Color.White)
    .borderRadius(Constants.EXAM_ITEM_BORDER_RADIUS)
  }
}