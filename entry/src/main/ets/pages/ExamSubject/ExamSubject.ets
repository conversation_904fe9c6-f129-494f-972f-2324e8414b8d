import { AxiosResponse } from '@ohos/axios';
import { getAllSubject } from '../../api/exam';
import { Constants } from '../../common/Constants'
import { ExamSubjectModel } from '../../models/ExamModel';
import Logger from '../../utils/Logger';
import { SubjectItem } from './SubjectItem';

const TAG = 'ExamSubject'

@Component
export struct ExamSubject {

  @State subjectList: ExamSubjectModel[] = [
    {
      id: 0,
      name: '鸿蒙初级认证',
      total_exams: 10,
      finished_count: 6
    },
    {
      id: 1,
      name: '鸿蒙中级认证',
      total_exams: 10,
      finished_count: 6
    },
    {
      id: 2,
      name: '鸿蒙高级认证',
      total_exams: 10,
      finished_count: 6
    },
    {
      id: 3,
      name: '鸿蒙专家认证',
      total_exams: 10,
      finished_count: 6
    }
  ];
  @Consume pageStack: NavPathStack;

  @LocalStorageProp(Constants.STORAGE_FONT_SIZE_KEY) fontSize: number = 0;

  async aboutToAppear(): Promise<void> {
    try {
      console.log('ExamSubject: Loading subject data...')
      const res: AxiosResponse = await getAllSubject()
      this.subjectList = res.data
      console.log('ExamSubject: Subject data loaded:', this.subjectList.length, 'items')
    } catch (err) {
      console.error('ExamSubject: Error loading subject data:', err)
      Logger.error(TAG, 'aboutToAppear getAllSubject error: ' + JSON.stringify(err))
    }
  }

  build() {
    NavDestination() {
      Flex({ direction: FlexDirection.Column }) {
        Text($r('app.string.exam_subject_list'))
          .width(Constants.FULL_PARENT)
          .fontSize(Constants.SUBJECT_TITLE_FONT_SIZE + this.fontSize)
          .fontWeight(FontWeight.Bold)
          .fontColor(Color.White)
          .textAlign(TextAlign.Center)
          .margin({ top: Constants.MARGIN_20, bottom: Constants.MARGIN_20 })
        Grid() {
          ForEach(this.subjectList, (item: ExamSubjectModel) => {
            GridItem() {
              SubjectItem({ item })
            }
          })
        }
        .width(Constants.FULL_PARENT)
        .columnsTemplate('1fr 1fr')
        .columnsGap(Constants.GRID_COLUMNS_GAP_SM)
        .rowsGap(Constants.GRID_ROWS_GAP_SM)
        .padding({ left: Constants.PADDING_20, right: Constants.PADDING_20 })
      }
      .width(Constants.FULL_PARENT)
      .height(Constants.FULL_PARENT)
      .backgroundColor($r('app.color.exam_subject_background'))
    }
    .hideTitleBar(true)

  }
}