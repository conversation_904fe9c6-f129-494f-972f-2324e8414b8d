import { Constants } from '../../common/Constants'
import { CustomInputComp } from '../../components/CustomInputComp'
import { promptAction, router } from '@kit.ArkUI'
import { UserInfoModel } from '../../models/UserInfoModel'
import axios, { AxiosResponse } from '@ohos/axios'
import { LocalPreferences } from '../../utils/LocalPreferences'

let storage = LocalStorage.getShared()

class RequestParams {
  username: string
  password: string
  avatar: string

  constructor(username: string, password: string, avatar: string = '') {
    this.username = username
    this.password = password
    this.avatar = avatar
  }
}

class ResponseData {
  message: string = ''
  code: number = 0
  token: string = ''
  data: UserInfoModel = new UserInfoModel()
}

@Entry(storage)
@Component
struct LoginPage {
  @State isReg: boolean = false
  @State username: string = 'admin'
  @State password: string = '123456'
  @State confirm_pwd: string = ''

  @LocalStorageProp(Constants.STORAGE_FONT_SIZE_KEY) fontSize: number = 0

  build() {
    Column() {
      Image($r('app.media.logo'))
        .width(Constants.LOGO_WIDTH)
        .aspectRatio(1)

      Text($r('app.string.Welcome'))
        .fontSize(Constants.TITLE_SIZE + this.fontSize)
        .fontWeight(FontWeight.Bold)
        .margin({
          top: Constants.LOGO_TITLE_TOP_MARGIN,
          bottom: Constants.LOGO_TITLE_BOTTOM_MARGIN
        })

      Column({
        space: Constants.ITEM_SPACE
      }) {
        Row() {
          Text($r('app.string.Login'))
            .fontSize(Constants.NORMAL_TEXT_FONT_SIZE + this.fontSize)
            .fontWeight(FontWeight.Bold)
            .fontColor(this.isReg ? $r('app.color.black_color') : $r('app.color.primary_color'))
            .onClick(() => {
              this.isReg = false
            })
          Text($r('app.string.Reg'))
            .fontSize(Constants.NORMAL_TEXT_FONT_SIZE + this.fontSize)
            .fontWeight(FontWeight.Bold)
            .fontColor(this.isReg ? $r('app.color.primary_color') : $r('app.color.black_color'))
            .onClick(() => {
              this.isReg = true
            })
        }
        .width(Constants.FULL_PARENT)
        .justifyContent(FlexAlign.SpaceAround)

        CustomInputComp({
          icon: $r('app.media.account_icon'),
          placeholder: $r('app.string.placeholder_phone'),
          text: $username
        })

        CustomInputComp({
          icon: $r('app.media.lock_icon'),
          placeholder: $r('app.string.placeholder_password'),
          type: InputType.Password,
          text: $password
        })

        if (this.isReg) {
          CustomInputComp({
            icon: $r('app.media.lock_icon'),
            placeholder: $r('app.string.placeholder_password_confirm'),
            type: InputType.Password,
            text: $confirm_pwd
          })
        }

        Button(this.isReg ? $r('app.string.Reg') : $r('app.string.Login'))
          .fontSize(Constants.BUTTON_FONT_SIZE + this.fontSize)
          .width(Constants.FULL_PARENT)
          .height(Constants.BUTTON_HEIGHT)
          .margin({
            top: Constants.ITEM_SPACE
          })
          .linearGradient({
            direction: GradientDirection.Right,
            colors: [
              ['#70b8f8', 0.4],
              ['#416ff5', 1]
            ]
          })
          .onClick(() => {
            console.log('Login button clicked, isReg:', this.isReg)
            console.log('Username:', this.username, 'Password:', this.password)
            this.doLoginOrRegister();
          })
      }
      .width(Constants.FULL_PARENT)
    }
    .width(Constants.FULL_PARENT)
    .height(Constants.FULL_PARENT)
    .backgroundImage($r('app.media.login_bg'))
    .backgroundImageSize(ImageSize.FILL)
    .padding(Constants.LOGIN_PAGE_PADDING)
    .justifyContent(FlexAlign.Center)
  }

  async doLoginOrRegister() {
    console.log('doLoginOrRegister called, isReg:', this.isReg)
    console.log('Username:', this.username, 'Password:', this.password)
    
    // 非空判断
    if (this.username === '' || this.password === '') {
      console.log('Empty username or password')
      promptAction.showToast({ message: $r('app.string.msg_no_empty') });
      return;
    }

    if (this.isReg) {
      // 注册请求
      if (this.confirm_pwd === '') {
        promptAction.showToast({ message: $r('app.string.confirm_pwd_no_empty') });
        return
      }
      if (this.password !== this.confirm_pwd) {
        promptAction.showToast({ message: $r('app.string.pwd_not_same') });
        return
      }
      try {
        const res: AxiosResponse<ResponseData> = await axios({
          url: `${Constants.SERVER}/api/register`,
          method: 'post',
          data: new RequestParams(this.username, this.password)
        })
        switch (res.data.code) {
          case 201:
            promptAction.showToast({ message: $r('app.string.register_success') })
            break
          case 400:
            promptAction.showToast({ message: $r('app.string.account_exist') })
            break
          default:
            promptAction.showToast({ message: $r('app.string.server_error') })
            break
        }
      } catch (e) {
        promptAction.showToast({ message: $r('app.string.server_error') + e.message })
        return;
      }
    } else {
      // 登录请求
      console.log('Starting login request...')
      console.log('Server URL:', Constants.SERVER)
      try {
        const res: AxiosResponse<ResponseData> = await axios({
          url: `${Constants.SERVER}/api/login`,
          method: 'post',
          data: new RequestParams(this.username, this.password)
        })
        console.log('Login response:', res.data)
        if (res.data.code === 200) {
          console.log('Login successful')
          LocalPreferences.setToken(res.data.token) // 保存服务器返回的token
          LocalPreferences.setUserInfo(res.data.data) // 保存用户信息
          promptAction.showToast({ message: $r('app.string.login_success'), duration: 1000 })
          setTimeout(() => {
            // 跳转到首页
            router.replaceUrl({
              url: Constants.HOME_PATH
            })
          })
        } else {
          console.log('Login failed, code:', res.data.code)
          promptAction.showToast({ message: $r('app.string.login_fail') })
        }
      } catch (e) {
        console.error('Login request failed:', e)
        promptAction.showToast({ message: $r('app.string.server_error') + (e.message || '') })
      }
    }
  }
}
