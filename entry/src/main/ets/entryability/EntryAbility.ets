import { AbilityConstant, UIAbility, Want } from '@kit.AbilityKit';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { window } from '@kit.ArkUI';
import { LocalPreferences } from '../utils/LocalPreferences';
import { Constants } from '../common/Constants';
import Logger from '../utils/Logger';

const TAG = 'EntryAbility';

export default class EntryAbility extends UIAbility {
  private selectPage: string = '';  // 用于存储跳转页面的名称
  private windowStage: window.WindowStage | null = null; // 保存窗口阶段实例
  private examId: number = -1; // 存储试卷 ID，默认为 -1

  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    this.dealCardParams(want);
  }

  onNewWant(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    this.dealCardParams(want);
  }

  private dealCardParams(want: Want) {
    let params: Record<string, Object> = {};
    try {
      const paramsString = want?.parameters?.params as string ?? '';
      if (paramsString) {
        params = JSON.parse(paramsString);
      }
    } catch (error) {
      Logger.error(TAG, `Failed to parse JSON. Cause: ${error.message}`);
    }
    this.selectPage = params.targetPage as string;
    this.examId = params.examId as number;
    Logger.info(TAG, `dealCardParams selectPage: ${this.selectPage}, examId: ${this.examId}`);

    if (this.windowStage !== null) {
      this.onWindowStageCreate(this.windowStage);
    }
  }

  onWindowStageCreate(windowStage: window.WindowStage): void {
    // Main window is created, set main page for this ability
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onWindowStageCreate');
    // 将当前的窗口阶段实例赋值给类的成员变量，以便后续使用
    this.windowStage = windowStage;

    // 定义默认页面路径为登录页面路径
    let pagePath = Constants.LOGIN_PAGE_PATH;
    LocalPreferences.context = this.context;
    // 检查本地存储中是否有 token，用于判断用户是否已登录
    if (LocalPreferences.getToken()) {
      // 如果有 token，说明用户已登录，设置页面路径为主页路径
      pagePath = Constants.HOME_PATH;
    }
    let storage = new LocalStorage();
    storage.setOrCreate<number>(Constants.STORAGE_FONT_SIZE_KEY, LocalPreferences.getFontSize());

    LocalPreferences.subscribeFontSizeChange((newFontSize: number) => {
      storage.set(Constants.STORAGE_FONT_SIZE_KEY, newFontSize)
    })

    // 记录调试信息，显示选择页面和试卷 ID 的值
    Logger.debug(TAG, `onWindowStageCreate: selectPage:
                  ${this.selectPage}, examId: ${this.examId}`);

    // 检查 selectPage 是否为空，如果不为空则将其保存到本地存储中
    if (this.selectPage) {
      storage.setOrCreate<string>('selectPage', this.selectPage);
    }

    // 检查 examId 是否不等于 -1，表示存在有效的试卷 ID
    if (this.examId !== -1) {
      // 将试卷 ID 存储到本地存储中
      storage.setOrCreate<number>('examId', this.examId);
    }
    windowStage.loadContent(pagePath, storage, (err) => {
      if (err.code) {
        hilog.error(0x0000, 'testTag', 'Failed to load the content. Cause: %{public}s', JSON.stringify(err) ?? '');
        return;
      }
      hilog.info(0x0000, 'testTag', 'Succeeded in loading the content.');
    });
  }

  onDestroy(): void {
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onDestroy');
  }

  onForeground(): void {
    // Ability has brought to foreground
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onForeground');
  }

  onBackground(): void {
    // Ability has back to background
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onBackground');
  }
}
