import preferences from '@ohos.data.preferences'
import { Constants } from "../common/Constants"
import { UserInfoModel } from '../models/UserInfoModel'

export class LocalPreferences {
  static context?: Context
  private static dataPreferences?: preferences.Preferences

  private static getPreferences() {
    if (LocalPreferences.dataPreferences) {
      return LocalPreferences.dataPreferences
    }
    LocalPreferences.dataPreferences = preferences.getPreferencesSync(
      LocalPreferences.context || getContext(),
      { name: Constants.STORE_NAME }
    )
    return LocalPreferences.dataPreferences
  }

  // 获取 fontSize 的值，若不存在则返回 0；并保证返回值为 number 类型
  static getFontSize(): number {
    const store = LocalPreferences.getPreferences()
    const value = store.getSync(Constants.PREFER_FONT_SIZE_KEY, 0)
    if (typeof value === 'string') {
      return parseInt(value, 10)
    }
    return value as number
  }

  // 设置 fontSize 的值，并保存
  static async setFontSize(fontSize: number) {
    const store = LocalPreferences.getPreferences()
    store.putSync(Constants.PREFER_FONT_SIZE_KEY, fontSize)
    await store.flush()
  }

  // 订阅 fontSize 键值对变化的回调
  static subscribeFontSizeChange(callback: (newFontSize: number) => void) {
    const store = LocalPreferences.getPreferences()
    store.on('change', (key: string) => {
      if (key === Constants.PREFER_FONT_SIZE_KEY) {
        const newFontSize = LocalPreferences.getFontSize()
        callback(newFontSize)
      }
    })
  }

  static getToken() {
    const store = LocalPreferences.getPreferences()
    return store.getSync(Constants.TOKEN_KEY, "")
  }

  static async setToken(token: string) {
    const store = LocalPreferences.getPreferences()
    store.putSync(Constants.TOKEN_KEY, token)
    await store.flush()
  }

  static async removeToken() {
    const store = LocalPreferences.getPreferences()
    store.deleteSync(Constants.TOKEN_KEY)
    await store.flush()
  }

  static getUserInfo(): UserInfoModel {
    const store = LocalPreferences.getPreferences()
    return store.getSync(Constants.USER_KEY, new UserInfoModel) as UserInfoModel
  }

  static async setUserInfo(user: UserInfoModel) {
    const store = LocalPreferences.getPreferences()
    store.putSync(Constants.USER_KEY, user)
    await store.flush()
  }

  static async removeUserInfo() {
    const store = LocalPreferences.getPreferences()
    store.deleteSync(Constants.USER_KEY)
    await store.flush()
  }
}
