import hilog from '@ohos.hilog';

const APP_TAG = 'ShuaTiBao';

type LogArg = any;

function buildLogStr(args: LogArg[]): string {
  if (!args || !args.length) {
    return '';
  }

  return JSON.stringify(args);
}

class Logger {
  private domain: number;
  private format: string = `[${APP_TAG}] %{public}s`;

  constructor(domain: number = 0xFF00) {
    this.domain = domain;
  }

  log(tag: string, ...args: LogArg[]): void {
    hilog.info(this.domain, tag, this.format, buildLogStr(args));
  }

  debug(tag: string, ...args: LogArg[]): void {
    hilog.debug(this.domain, tag, this.format, buildLogStr(args));
  }

  info(tag: string, ...args: LogArg[]): void {
    hilog.info(this.domain, tag, this.format, buildLogStr(args));
  }

  warn(tag: string, ...args: LogArg[]): void {
    hilog.warn(this.domain, tag, this.format, buildLogStr(args));
  }

  error(tag: string, ...args: LogArg[]): void {
    hilog.error(this.domain, tag, this.format, buildLogStr(args));
  }

  d(tag: string, ...args: LogArg[]): void {
    hilog.debug(this.domain, tag, this.format, buildLogStr(args));
  }

  i(tag: string, ...args: LogArg[]): void {
    hilog.info(this.domain, tag, this.format, buildLogStr(args));
  }

  w(tag: string, ...args: LogArg[]): void {
    hilog.warn(this.domain, tag, this.format, buildLogStr(args));
  }

  e(tag: string, ...args: LogArg[]): void {
    hilog.error(this.domain, tag, this.format, buildLogStr(args));
  }
}

export default new Logger(0xFF00);
