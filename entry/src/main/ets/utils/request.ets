import axios, { InternalAxiosRequestConfig, AxiosError, AxiosResponse } from '@ohos/axios'
import { Constants } from '../common/Constants';
import { LocalPreferences } from './LocalPreferences'
import { router } from '@kit.ArkUI';

// 创建自定义的 axios 实例，用于配置默认的设置
const instance = axios.create({
  baseURL: Constants.SERVER // 设置默认的请求地址，后续请求可以不用再写
});

// 添加请求拦截器：请求拦截器的作用是，在每次请求发送之前，检查本地存储是否有 Token，
// 如果有，则将 Token 添加到请求头的 Authorization 字段。这样可以实现全局身份认证，避免在每个请求中都手动添加 Token。
instance.interceptors.request.use((config: InternalAxiosRequestConfig): InternalAxiosRequestConfig => {

  // 获取首选项里的 Token
  const token = LocalPreferences.getToken()
  if (token) {
    // 将 Token 添加到请求头的 Authorization 字段
    config.headers.Authorization = `Bearer ${token}`
  }
  return config;
}, (error: AxiosError) => {
  return Promise.reject(error);
});


// 添加响应拦截器：响应拦截器的作用是，在每次请求返回之后，检查响应的状态码，
instance.interceptors.response.use((response: AxiosResponse): AxiosResponse => {
  return response;
}, (error: AxiosError) => {
  // 如果接口返回 401 状态码（通常表示 Token 过期），就自动跳转到登录页面。
  if (error.response?.status === 401) {
    router.pushUrl({
      url: Constants.LOGIN_PAGE_PATH
    })
  }
  return Promise.reject(error);
});
// 将配置好的 axios 实例导出，使得其他模块可以直接引用该实例进行网络请求。
export default instance