import { ExamModel } from '../../models/ExamModel'
let storage = LocalStorage.getShared()
@Entry(storage)
@Component
struct ExamListWidgetCard {
  /*
   * 定义卡片的标题
   */
  readonly TITLE: string = '试卷列表';

  /*
   * 定义动作类型，用于标识用户的操作类型
   */
  readonly ACTION_TYPE: string = 'router';

  /*
   * 定义目标 Ability 名称
   */
  readonly ABILITY_NAME: string = 'EntryAbility';

  /*
   * 设置宽度和高度百分比，确保卡片充满整个区域
   */
  readonly FULL_WIDTH_PERCENT: string = '100%';
  readonly FULL_HEIGHT_PERCENT: string = '100%';

  /*
   * 定义选择的页面和试卷 ID，初始值为空或 0
   */
  private selectPage: string = '';
  private examId: number = 0;

  /*
   * 定义试卷列表，使用 LocalStorageProp 装饰器与本地存储绑定，属性名称为 'examList'
   */
  @LocalStorageProp('examList') examList: ExamModel[] = [];

  // build 方法构建卡片整体界面
  build() {
    Row() { // 使用 Row 作为根布局容器
      Column() { // 垂直布局，包含标题和试卷列表
        Row() { // 标题行
          Text(this.TITLE)
            .width('100%')
            .fontSize(24)
            .fontWeight(700)
            .textAlign(TextAlign.Center)
            .layoutWeight(1)
            .margin({ top: 20, bottom: 10 });
        }

        List() { // 展示试卷列表
          ForEach(this.examList, (item: ExamModel) => {
            ListItem() {
              this.ExamListItem(item); // 调用子组件生成每个试卷条目
            }
          });
        }
        .width('100%')
        .height('100%')
        .layoutWeight(1)
        .borderRadius({ topLeft: 35, topRight: 35 })
        .backgroundColor(Color.White);
      }
      .width(this.FULL_WIDTH_PERCENT);
    }
    .height(this.FULL_HEIGHT_PERCENT)
    .backgroundColor('#ecf3fd') // 设置卡片背景色
    .onClick(() => {
      // 点击卡片空白区域时清除选择状态并重置页面
      this.selectPage = '';
      this.examId = -1;
      postCardAction(this, {
        action: this.ACTION_TYPE,
        abilityName: this.ABILITY_NAME,
        params: {
          targetPage: this.selectPage,
          examId: this.examId
        }
      });
    });
  }

  // 定义一个方法，用于构建单个试卷条目的 UI
  @Builder
  ExamListItem(exam: ExamModel) {
    Column() {
      Row() { // 水平布局，包含图标、标题和状态图标
        Image(exam.total_questions ? $r('app.media.unlock') : $r('app.media.lock'))
          .width(65); // 根据是否有题目显示不同的图标

        Text(exam.title) // 显示试卷的标题
          .fontSize(18)
          .fontWeight(700)
          .layoutWeight(1)
          .margin({ left: 20, right: 20 });

        Image(exam.total_questions ? $r('app.media.havaStudy') : $r('app.media.unStudy'))
          .width(55)
          .alignSelf(ItemAlign.End); // 根据题目状态显示图标
      }
      .width('100%')
      .height(65)
      .justifyContent(FlexAlign.SpaceBetween);

      Divider() // 分隔线
        .color('#3e3e3e');
    }
    .width('100%')
    .padding(15)
    .onClick(() => {
      // 点击事件，跳转到试卷详情页
      this.selectPage = 'ExamDetail';
      this.examId = exam.id;
      postCardAction(this, {
        action: this.ACTION_TYPE,
        abilityName: this.ABILITY_NAME,
        params: {
          targetPage: this.selectPage,
          examId: this.examId
        }
      });
    });
  }
}