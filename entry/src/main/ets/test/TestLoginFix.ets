import { Constants } from '../common/Constants'
import { CustomInputComp } from '../components/CustomInputComp'
import { promptAction } from '@kit.ArkUI'

@Entry
@Component
struct TestLoginFix {
  @State username: string = 'admin'
  @State password: string = '123456'
  @State isReg: boolean = false

  build() {
    Column({ space: Constants.SPACE_SIZE }) {
      Text('登录功能测试')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin({ bottom: 20 })

      CustomInputComp({
        icon: $r('app.media.account_icon'),
        placeholder: '请输入账号',
        text: $this.username
      })

      CustomInputComp({
        icon: $r('app.media.lock_icon'),
        placeholder: '请输入密码',
        type: InputType.Password,
        text: $this.password
      })

      Button('测试登录')
        .fontSize(16)
        .width('100%')
        .height(50)
        .margin({ top: 20 })
        .onClick(() => {
          console.log('Test login button clicked')
          console.log('Username:', this.username, 'Password:', this.password)
          
          if (this.username === '' || this.password === '') {
            promptAction.showToast({ message: '用户名和密码不能为空' });
            return;
          }
          
          promptAction.showToast({ 
            message: `登录测试成功！用户名: ${this.username}, 密码: ${this.password}` 
          });
        })

      Text(`当前输入值:`)
        .fontSize(14)
        .margin({ top: 20 })
      
      Text(`用户名: ${this.username}`)
        .fontSize(12)
        .fontColor(Color.Gray)
      
      Text(`密码: ${this.password}`)
        .fontSize(12)
        .fontColor(Color.Gray)
    }
    .height('100%')
    .width('100%')
    .padding(20)
    .justifyContent(FlexAlign.Center)
  }
}
