import { Constants } from '../common/Constants'
import Logger from '../utils/Logger'

const TAG = 'TestLogger';

@Entry
@Component
struct TestLogger {
  build() {
    Column({ space: Constants.SPACE_SIZE }) {

      Button('日志测试')
        .onClick(() => {
          Logger.debug(TAG, 'debug', 1);
          Logger.info(TAG, 'Info', 2);
          Logger.warn(TAG, 'warn', 3);
          Logger.error(TAG, 'error', 4);
        })
    }
    .height('100%')
    .width('100%')
  }
}