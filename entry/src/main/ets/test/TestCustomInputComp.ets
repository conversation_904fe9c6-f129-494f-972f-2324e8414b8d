import { Constants } from '../common/Constants'
import { CustomInputComp } from '../components/CustomInputComp'
import Logger from '../utils/Logger'

const TAG = 'TestCustomInputComp'
@Entry
@Component
struct TestCustomInputComp {
  @State username: string = 'admin'
  @State password: string = '123456'
  build() {
    Column({ space: Constants.SPACE_SIZE }) {
      CustomInputComp({
        icon: $r('app.media.account_icon'),
        placeholder: '请输入账号',
        text: this.username
      })

      CustomInputComp({
        icon: $r('app.media.lock_icon'),
        placeholder: '请输入密码',
        type:InputType.Password,
        text: this.password
      })
      Button('获取用户名和密码')
        .onClick(() => {
          Logger.info(TAG, `username: ${this.username}, password: ${this.password}`)
        })
    }
    .height('100%')
    .width('100%')
    .padding({ left: 20, right: 20 })
  }
}