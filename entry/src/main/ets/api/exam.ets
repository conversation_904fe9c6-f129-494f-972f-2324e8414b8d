import request from '../utils/request'
import { AxiosResponse } from '@ohos/axios'

export function getAllSubject(): Promise<AxiosResponse> {
  return request.get(`/api/subjects`)
}

export function randomExam(limit: number): Promise<AxiosResponse> {
  return request.get(`/api/subjects/randomexams`, { params: { limit: limit } })
}

export function getExamById(subject_id: number): Promise<AxiosResponse> {
  return request.get(`/api/subjects/${subject_id}/exams`)
}

export function getQuestion(examId: number | string): Promise<AxiosResponse> {
  return request.get(`api/exams/${examId}/questions`)
}

class AnswerQuestionData {
  questionId: number | string
  userAnswer: string

  constructor(questionId: number | string, userAnswer: string) {

    this.questionId = questionId
    this.userAnswer = userAnswer
  }
}

// 回答某道题
export function answerQuestion(questionId: number | string, userAnswer: string): Promise<AxiosResponse> {
  return request.post(`api/useranswer/submit`, new AnswerQuestionData(questionId, userAnswer))
}

// 交卷
export function submitExam(examId: number | string): Promise<AxiosResponse> {
  return request.post(`api/userexams/${examId}/submit`)
}

// 查询是否做过本套试卷
export function queryExamSubmit(examId: number | string): Promise<AxiosResponse> {
  return request.get(`api/userexams/${examId}`)
}

// 重新做题
export function deleteAnswer(examId: number | string): Promise<AxiosResponse> {
  return request.delete(`api/userexams/${examId}`)
}