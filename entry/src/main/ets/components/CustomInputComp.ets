import { Constants } from "../common/Constants"

@Component
export struct CustomInputComp {
  // 组件的属性
  placeholder: ResourceStr = ''
  icon: ResourceStr = ''
  type: InputType = InputType.Normal
  @Link text: string

  build() {
    Row() {
      // 显示图标
      if (this.icon) {
        Image(this.icon)
          .width(Constants.PREFIX_ICON_WIDTH)
      }

      // 输入框
      TextInput({
        placeholder: this.placeholder,
        text: $$this.text
      })
        .type(this.type)
        .maxLength(Constants.LOGIN_INPUT_MAX_LEN)
        .showPasswordIcon(false)
        .backgroundColor(Color.Transparent)
    }
    .backgroundColor(Constants.LIGHT_GRAY)
    .height(Constants.INPUT_HEIGHT)
    .borderRadius('50%')
    .padding({ left: Constants.SPACE_SIZE })
  }
}