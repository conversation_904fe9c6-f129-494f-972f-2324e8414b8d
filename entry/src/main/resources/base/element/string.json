{"string": [{"name": "module_desc", "value": "module description"}, {"name": "EntryAbility_desc", "value": "description"}, {"name": "EntryAbility_label", "value": "刷题宝"}, {"name": "exam_list", "value": "习题列表"}, {"name": "doing_exercises", "value": "在线答题"}, {"name": "remaining_questions", "value": "剩余题数："}, {"name": "answer_card", "value": "答题卡"}, {"name": "question", "value": "题目"}, {"name": "re_answer", "value": "重新刷题"}, {"name": "Welcome", "value": "欢迎使用刷题宝"}, {"name": "<PERSON><PERSON>", "value": "登录"}, {"name": "Reg", "value": "注册"}, {"name": "placeholder_phone", "value": "请输入手机号"}, {"name": "placeholder_password", "value": "请输入密码"}, {"name": "placeholder_password_confirm", "value": "请确认密码"}, {"name": "answered_questions", "value": "已刷题目"}, {"name": "wrong_questions", "value": "错题数量"}, {"name": "exam_subject_list", "value": "科目列表"}, {"name": "begin_exam", "value": "开始做题"}, {"name": "msg_no_empty", "value": "用户名或者密码不能为空！"}, {"name": "msg_login_success", "value": "登录成功！"}, {"name": "exam_detail", "value": "在线答题"}, {"name": "submit", "value": "交卷"}, {"name": "answer_result", "value": "答题结果"}, {"name": "total_question", "value": "总题数"}, {"name": "right_answer", "value": "答对"}, {"name": "wrong_answer", "value": "答错"}, {"name": "font_size_setting", "value": "字体大小"}, {"name": "recommend", "value": "推荐"}, {"name": "exam", "value": "刷题"}, {"name": "user_center", "value": "个人中心"}, {"name": "random_exam_title", "value": "随机试卷"}, {"name": "msg_pwd_not_same", "value": "两次输入的密码不一致"}, {"name": "confirm_pwd_no_empty", "value": "验证密码不能为空"}, {"name": "pwd_not_same", "value": "两次输入的密码不一致"}, {"name": "register_success", "value": "注册成功！"}, {"name": "account_exist", "value": "账号已存在！"}, {"name": "server_error", "value": "服务器内部错误！"}, {"name": "login_success", "value": "登录成功！"}, {"name": "login_fail", "value": "账号或者密码错误"}, {"name": "EntryFormAbility_desc", "value": "form_description"}, {"name": "EntryFormAbility_label", "value": "form_label"}, {"name": "ExamListWidget_desc", "value": "This widget diplays a list of exams."}, {"name": "ExamListWidget_display_name", "value": "ExamList"}]}